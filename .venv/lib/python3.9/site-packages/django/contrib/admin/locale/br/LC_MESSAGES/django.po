# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# Irriep Nala <PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-01-16 20:42+0100\n"
"PO-Revision-Date: 2019-01-18 00:36+0000\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language-Team: Breton (http://www.transifex.com/django/django/language/br/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: br\n"
"Plural-Forms: nplurals=5; plural=((n%10 == 1) && (n%100 != 11) && (n%100 !"
"=71) && (n%100 !=91) ? 0 :(n%10 == 2) && (n%100 != 12) && (n%100 !=72) && (n"
"%100 !=92) ? 1 :(n%10 ==3 || n%10==4 || n%10==9) && (n%100 < 10 || n% 100 > "
"19) && (n%100 < 70 || n%100 > 79) && (n%100 < 90 || n%100 > 99) ? 2 :(n != 0 "
"&& n % 1000000 == 0) ? 3 : 4);\n"

#, python-format
msgid "Successfully deleted %(count)d %(items)s."
msgstr ""

#, python-format
msgid "Cannot delete %(name)s"
msgstr ""

msgid "Are you sure?"
msgstr "Ha sur oc'h?"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "Dilemel %(verbose_name_plural)s diuzet"

msgid "Administration"
msgstr "Melestradurezh"

msgid "All"
msgstr "An holl"

msgid "Yes"
msgstr "Ya"

msgid "No"
msgstr "Ket"

msgid "Unknown"
msgstr "Dianav"

msgid "Any date"
msgstr "Forzh pegoulz"

msgid "Today"
msgstr "Hiziv"

msgid "Past 7 days"
msgstr "Er 7 devezh diwezhañ"

msgid "This month"
msgstr "Ar miz-mañ"

msgid "This year"
msgstr "Ar bloaz-mañ"

msgid "No date"
msgstr "Deiziad ebet"

msgid "Has date"
msgstr "D'an deiziad"

#, python-format
msgid ""
"Please enter the correct %(username)s and password for a staff account. Note "
"that both fields may be case-sensitive."
msgstr ""

msgid "Action:"
msgstr "Ober:"

#, python-format
msgid "Add another %(verbose_name)s"
msgstr "Ouzhpennañ %(verbose_name)s all"

msgid "Remove"
msgstr "Lemel kuit"

msgid "Addition"
msgstr "Sammañ"

msgid "Change"
msgstr "Cheñch"

msgid "Deletion"
msgstr "Diverkadur"

msgid "action time"
msgstr "eur an ober"

msgid "user"
msgstr "implijer"

msgid "content type"
msgstr "doare endalc'had"

msgid "object id"
msgstr "id an objed"

#. Translators: 'repr' means representation
#. (https://docs.python.org/library/functions.html#repr)
msgid "object repr"
msgstr ""

msgid "action flag"
msgstr "ober banniel"

msgid "change message"
msgstr "Kemennadenn cheñchamant"

msgid "log entry"
msgstr ""

msgid "log entries"
msgstr ""

#, python-format
msgid "Added \"%(object)s\"."
msgstr "Ouzhpennet \"%(object)s\"."

#, python-format
msgid "Changed \"%(object)s\" - %(changes)s"
msgstr "Cheñchet \"%(object)s\" - %(changes)s"

#, python-format
msgid "Deleted \"%(object)s.\""
msgstr "Dilamet \"%(object)s.\""

msgid "LogEntry Object"
msgstr ""

#, python-brace-format
msgid "Added {name} \"{object}\"."
msgstr "Ouzhpennet {name} \"{object}\"."

msgid "Added."
msgstr "Ouzhpennet."

msgid "and"
msgstr "ha"

#, python-brace-format
msgid "Changed {fields} for {name} \"{object}\"."
msgstr "Cheñchet {fields} evit {name} \"{object}\"."

#, python-brace-format
msgid "Changed {fields}."
msgstr "Cheñchet {fields}."

#, python-brace-format
msgid "Deleted {name} \"{object}\"."
msgstr "Dilamet {name} \"{object}\"."

msgid "No fields changed."
msgstr "Maezienn ebet cheñchet."

msgid "None"
msgstr "Hini ebet"

msgid ""
"Hold down \"Control\", or \"Command\" on a Mac, to select more than one."
msgstr ""

#, python-brace-format
msgid "The {name} \"{obj}\" was added successfully."
msgstr ""

msgid "You may edit it again below."
msgstr "Rankout a rit ec'h aozañ adarre dindan."

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was added successfully. You may add another {name} "
"below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was changed successfully. You may edit it again below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was added successfully. You may edit it again below."
msgstr ""

#, python-brace-format
msgid ""
"The {name} \"{obj}\" was changed successfully. You may add another {name} "
"below."
msgstr ""

#, python-brace-format
msgid "The {name} \"{obj}\" was changed successfully."
msgstr ""

msgid ""
"Items must be selected in order to perform actions on them. No items have "
"been changed."
msgstr ""

msgid "No action selected."
msgstr "Ober ebet diuzet."

#, python-format
msgid "The %(name)s \"%(obj)s\" was deleted successfully."
msgstr ""

#, python-format
msgid "%(name)s with ID \"%(key)s\" doesn't exist. Perhaps it was deleted?"
msgstr ""

#, python-format
msgid "Add %s"
msgstr "Ouzhpennañ %s"

#, python-format
msgid "Change %s"
msgstr "Cheñch %s"

#, python-format
msgid "View %s"
msgstr "Gwelet %s"

msgid "Database error"
msgstr "Fazi diaz-roadennoù"

#, python-format
msgid "%(count)s %(name)s was changed successfully."
msgid_plural "%(count)s %(name)s were changed successfully."
msgstr[0] "%(count)s %(name)s  a zo bet cheñchet mat."
msgstr[1] "%(count)s %(name)s a zo bet cheñchet mat. "
msgstr[2] "%(count)s %(name)s a zo bet cheñchet mat. "
msgstr[3] "%(count)s %(name)s a zo bet cheñchet mat."
msgstr[4] "%(count)s %(name)s a zo bet cheñchet mat."

#, python-format
msgid "%(total_count)s selected"
msgid_plural "All %(total_count)s selected"
msgstr[0] "%(total_count)s diuzet"
msgstr[1] "%(total_count)s diuzet"
msgstr[2] "%(total_count)s diuzet"
msgstr[3] "%(total_count)s diuzet"
msgstr[4] "Pep %(total_count)s diuzet"

#, python-format
msgid "0 of %(cnt)s selected"
msgstr "0 diwar %(cnt)s diuzet"

#, python-format
msgid "Change history: %s"
msgstr "Istor ar cheñchadurioù: %s"

#. Translators: Model verbose name and instance representation,
#. suitable to be an item in a list.
#, python-format
msgid "%(class_name)s %(instance)s"
msgstr "%(class_name)s %(instance)s"

#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""

msgid "Django site admin"
msgstr "Lec'hienn verañ Django"

msgid "Django administration"
msgstr "Merañ Django"

msgid "Site administration"
msgstr "Merañ al lec'hienn"

msgid "Log in"
msgstr "Kevreañ"

#, python-format
msgid "%(app)s administration"
msgstr ""

msgid "Page not found"
msgstr "N'eo ket bet kavet ar bajenn"

msgid "We're sorry, but the requested page could not be found."
msgstr ""

msgid "Home"
msgstr "Degemer"

msgid "Server error"
msgstr "Fazi servijer"

msgid "Server error (500)"
msgstr "Fazi servijer (500)"

msgid "Server Error <em>(500)</em>"
msgstr "Fazi servijer <em>(500)</em>"

msgid ""
"There's been an error. It's been reported to the site administrators via "
"email and should be fixed shortly. Thanks for your patience."
msgstr ""

msgid "Run the selected action"
msgstr ""

msgid "Go"
msgstr "Mont"

msgid "Click here to select the objects across all pages"
msgstr ""

#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr ""

msgid "Clear selection"
msgstr "Riñsañ an diuzadenn"

msgid ""
"First, enter a username and password. Then, you'll be able to edit more user "
"options."
msgstr ""

msgid "Enter a username and password."
msgstr "Merkit un anv implijer hag ur ger-tremen."

msgid "Change password"
msgstr "Cheñch ger-tremen"

msgid "Please correct the error below."
msgstr ""

msgid "Please correct the errors below."
msgstr ""

#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""

msgid "Welcome,"
msgstr "Degemer mat,"

msgid "View site"
msgstr ""

msgid "Documentation"
msgstr "Teulioù"

msgid "Log out"
msgstr "Digevreañ"

#, python-format
msgid "Add %(name)s"
msgstr "Ouzhpennañ %(name)s"

msgid "History"
msgstr "Istor"

msgid "View on site"
msgstr "Gwelet war al lec'hienn"

msgid "Filter"
msgstr "Sil"

msgid "Remove from sorting"
msgstr ""

#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr ""

msgid "Toggle sorting"
msgstr "Eilpennañ an diuzadenn"

msgid "Delete"
msgstr "Diverkañ"

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""

#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""

#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""

msgid "Objects"
msgstr ""

msgid "Yes, I'm sure"
msgstr "Ya, sur on"

msgid "No, take me back"
msgstr ""

msgid "Delete multiple objects"
msgstr ""

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""

#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""

#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""

msgid "View"
msgstr ""

msgid "Delete?"
msgstr "Diverkañ ?"

#, python-format
msgid " By %(filter_title)s "
msgstr " dre %(filter_title)s "

msgid "Summary"
msgstr ""

#, python-format
msgid "Models in the %(name)s application"
msgstr ""

msgid "Add"
msgstr "Ouzhpennañ"

msgid "You don't have permission to view or edit anything."
msgstr ""

msgid "Recent actions"
msgstr ""

msgid "My actions"
msgstr ""

msgid "None available"
msgstr ""

msgid "Unknown content"
msgstr "Endalc'had dianav"

msgid ""
"Something's wrong with your database installation. Make sure the appropriate "
"database tables have been created, and make sure the database is readable by "
"the appropriate user."
msgstr ""

#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""

msgid "Forgotten your password or username?"
msgstr "Disoñjet ho ker-tremen pe hoc'h anv implijer ganeoc'h ?"

msgid "Date/time"
msgstr "Deiziad/eur"

msgid "User"
msgstr "Implijer"

msgid "Action"
msgstr "Ober"

msgid ""
"This object doesn't have a change history. It probably wasn't added via this "
"admin site."
msgstr ""

msgid "Show all"
msgstr "Diskouez pep tra"

msgid "Save"
msgstr "Enrollañ"

msgid "Popup closing…"
msgstr ""

msgid "Search"
msgstr "Klask"

#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""
msgstr[3] ""
msgstr[4] ""

#, python-format
msgid "%(full_result_count)s total"
msgstr ""

msgid "Save as new"
msgstr "Enrollañ evel nevez"

msgid "Save and add another"
msgstr "Enrollañ hag ouzhpennañ unan all"

msgid "Save and continue editing"
msgstr "Enrollañ ha derc'hel da gemmañ"

msgid "Save and view"
msgstr ""

msgid "Close"
msgstr ""

#, python-format
msgid "Change selected %(model)s"
msgstr ""

#, python-format
msgid "Add another %(model)s"
msgstr ""

#, python-format
msgid "Delete selected %(model)s"
msgstr ""

msgid "Thanks for spending some quality time with the Web site today."
msgstr ""

msgid "Log in again"
msgstr "Kevreañ en-dro"

msgid "Password change"
msgstr "Cheñch ho ker-tremen"

msgid "Your password was changed."
msgstr "Cheñchet eo bet ho ker-tremen."

msgid ""
"Please enter your old password, for security's sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""

msgid "Change my password"
msgstr "Cheñch ma ger-tremen"

msgid "Password reset"
msgstr "Adderaouekaat ar ger-tremen"

msgid "Your password has been set.  You may go ahead and log in now."
msgstr ""

msgid "Password reset confirmation"
msgstr "Kadarnaat eo bet cheñchet ar ger-tremen"

msgid ""
"Please enter your new password twice so we can verify you typed it in "
"correctly."
msgstr ""

msgid "New password:"
msgstr "Ger-tremen nevez :"

msgid "Confirm password:"
msgstr "Kadarnaat ar ger-tremen :"

msgid ""
"The password reset link was invalid, possibly because it has already been "
"used.  Please request a new password reset."
msgstr ""

msgid ""
"We've emailed you instructions for setting your password, if an account "
"exists with the email you entered. You should receive them shortly."
msgstr ""

msgid ""
"If you don't receive an email, please make sure you've entered the address "
"you registered with, and check your spam folder."
msgstr ""

#, python-format
msgid ""
"You're receiving this email because you requested a password reset for your "
"user account at %(site_name)s."
msgstr ""

msgid "Please go to the following page and choose a new password:"
msgstr ""

msgid "Your username, in case you've forgotten:"
msgstr ""

msgid "Thanks for using our site!"
msgstr "Ho trugarekaat da ober gant hol lec'hienn !"

#, python-format
msgid "The %(site_name)s team"
msgstr ""

msgid ""
"Forgotten your password? Enter your email address below, and we'll email "
"instructions for setting a new one."
msgstr ""

msgid "Email address:"
msgstr ""

msgid "Reset my password"
msgstr ""

msgid "All dates"
msgstr "An holl zeiziadoù"

#, python-format
msgid "Select %s"
msgstr "Diuzañ %s"

#, python-format
msgid "Select %s to change"
msgstr ""

#, python-format
msgid "Select %s to view"
msgstr ""

msgid "Date:"
msgstr "Deiziad :"

msgid "Time:"
msgstr "Eur :"

msgid "Lookup"
msgstr "Klask"

msgid "Currently:"
msgstr ""

msgid "Change:"
msgstr ""
