{% extends 'site/base.html' %}
{% block content %}
<section class="product-list">
  <h3>{% if locale == 'zh' %}产品{% else %}Products{% endif %}</h3>
  {% if cat %}
    <div class="section-header" style="margin-top:8px">
      <div class="section-title">{% if locale == 'zh' %}室内{% else %}Indoor{% endif %}</div>
    </div>
    <div class="grid">
      {% for p in items %}{% if p.usage == 'indoor' %}
        <a class="product-card" href="/products/{{ p.id }}/">
          <img src="{{ p.cover }}" alt="{{ p.name }}">
          <h4>{{ p.name }}</h4>
          <p>{{ p.short_desc }}</p>
          <div class="tags">
            {% for t in p.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
          </div>
        </a>
      {% endif %}{% endfor %}
    </div>

    <div class="section-header" style="margin-top:20px">
      <div class="section-title">{% if locale == 'zh' %}室外{% else %}Outdoor{% endif %}</div>
    </div>
    <div class="grid">
      {% for p in items %}{% if p.usage == 'outdoor' %}
        <a class="product-card" href="/products/{{ p.id }}/">
          <img src="{{ p.cover }}" alt="{{ p.name }}">
          <h4>{{ p.name }}</h4>
          <p>{{ p.short_desc }}</p>
          <div class="tags">
            {% for t in p.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
          </div>
        </a>
      {% endif %}{% endfor %}
    </div>
  {% else %}
    <div class="grid">
      {% for p in items %}
        <a class="product-card" href="/products/{{ p.id }}/">
          <img src="{{ p.cover }}" alt="{{ p.name }}">
          <h4>{{ p.name }}</h4>
          <p>{{ p.short_desc }}</p>
          <div class="tags">
            {% for t in p.tags %}<span class="tag tag-{{ t }}">{{ t|upper }}</span>{% endfor %}
          </div>
        </a>
      {% empty %}
        <p>暂无产品</p>
      {% endfor %}
    </div>
  {% endif %}
</section>
{% endblock %}

