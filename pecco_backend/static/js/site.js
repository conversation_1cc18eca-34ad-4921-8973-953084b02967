(function(){
  // Basic fade carousel
  const carousels = document.querySelectorAll('.carousel');
  carousels.forEach(function(c){
    const slides = c.querySelectorAll('.slide');
    let idx = 0;
    slides.forEach((s,i)=>{ s.style.opacity = (i===0?1:0); s.style.transition='opacity 900ms ease, transform 900ms ease'; });
    function tick(){
      const prev = idx;
      idx = (idx + 1) % slides.length;
      slides[prev].style.opacity = 0;
      slides[idx].style.opacity = 1;
      slides[idx].style.transform = 'scale(1.02)';
      setTimeout(()=>{ slides[idx].style.transform = 'scale(1)'; }, 900);
    }
    if (slides.length > 1) setInterval(tick, 4200);
  });

  // Mobile menu functionality
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mobileNav = document.querySelector('.mobile-nav');
  const body = document.body;

  if (mobileMenuToggle && mobileNav) {
    mobileMenuToggle.addEventListener('click', function() {
      const isActive = mobileMenuToggle.classList.contains('active');

      if (isActive) {
        // Close menu
        mobileMenuToggle.classList.remove('active');
        mobileNav.classList.remove('active');
        body.style.overflow = '';
      } else {
        // Open menu
        mobileMenuToggle.classList.add('active');
        mobileNav.classList.add('active');
        body.style.overflow = 'hidden';
      }
    });

    // Close menu when clicking on a link
    const mobileNavLinks = mobileNav.querySelectorAll('a');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', function() {
        mobileMenuToggle.classList.remove('active');
        mobileNav.classList.remove('active');
        body.style.overflow = '';
      });
    });

    // Close menu when clicking outside
    mobileNav.addEventListener('click', function(e) {
      if (e.target === mobileNav) {
        mobileMenuToggle.classList.remove('active');
        mobileNav.classList.remove('active');
        body.style.overflow = '';
      }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileNav.classList.contains('active')) {
        mobileMenuToggle.classList.remove('active');
        mobileNav.classList.remove('active');
        body.style.overflow = '';
      }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      if (window.innerWidth > 768 && mobileNav.classList.contains('active')) {
        mobileMenuToggle.classList.remove('active');
        mobileNav.classList.remove('active');
        body.style.overflow = '';
      }
    });
  }

  // Lazy load images: add loading=lazy for non-first images
  document.querySelectorAll('img').forEach((img, i)=>{
    if (!img.getAttribute('loading')) {
      img.setAttribute('loading', i === 0 ? 'eager' : 'lazy');
    }
    img.setAttribute('decoding','async');
  });
})();

