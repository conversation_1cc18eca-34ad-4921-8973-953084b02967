(function(){
  // Basic fade carousel
  const carousels = document.querySelectorAll('.carousel');
  carousels.forEach(function(c){
    const slides = c.querySelectorAll('.slide');
    let idx = 0;
    slides.forEach((s,i)=>{ s.style.opacity = (i===0?1:0); s.style.transition='opacity 900ms ease, transform 900ms ease'; });
    function tick(){
      const prev = idx;
      idx = (idx + 1) % slides.length;
      slides[prev].style.opacity = 0;
      slides[idx].style.opacity = 1;
      slides[idx].style.transform = 'scale(1.02)';
      setTimeout(()=>{ slides[idx].style.transform = 'scale(1)'; }, 900);
    }
    if (slides.length > 1) setInterval(tick, 4200);
  });

  // Lazy load images: add loading=lazy for non-first images
  document.querySelectorAll('img').forEach((img, i)=>{
    if (!img.getAttribute('loading')) {
      img.setAttribute('loading', i === 0 ? 'eager' : 'lazy');
    }
    img.setAttribute('decoding','async');
  });
})();

